<script setup lang="ts">
import { type ECOption, useEcharts } from '@/hooks/common/echarts';

defineOptions({
  name: 'RightTopContainer'
});

interface Props {
  title: string;
  icon: string;
  cardType?: string;
  animationDelay?: number;
}

const props = withDefaults(defineProps<Props>(), {
  cardType: 'metric',
  animationDelay: 0
});

// 模拟数据
const chartData = {
  months: ['1月', '2月', '3月', '4月', '5月', '6月'],
  sales: [120, 200, 150, 80, 70, 110], // 销售额（柱状图）
  profit: [20, 30, 25, 15, 12, 22], // 利润（柱状图）
  growth: [15, 25, 18, 10, 8, 20] // 增长率（折线图）
};

// ECharts 配置
function createChartOptions(): ECOption {
  return {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        crossStyle: {
          color: '#999'
        }
      },
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      borderColor: '#777',
      textStyle: {
        color: '#fff'
      }
    },
    legend: {
      data: ['销售额', '利润', '增长率'],
      textStyle: {
        color: '#fff'
      },
      top: 10
    },
    xAxis: [
      {
        type: 'category',
        data: chartData.months,
        axisPointer: {
          type: 'shadow'
        },
        axisLabel: {
          color: '#fff'
        },
        axisLine: {
          lineStyle: {
            color: '#fff'
          }
        }
      }
    ],
    yAxis: [
      {
        type: 'value',
        name: '金额',
        min: 0,
        max: 250,
        interval: 50,
        axisLabel: {
          formatter: '{value}万',
          color: '#fff'
        },
        axisLine: {
          lineStyle: {
            color: '#fff'
          }
        },
        splitLine: {
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.1)'
          }
        }
      },
      {
        type: 'value',
        name: '增长率',
        min: 0,
        max: 30,
        interval: 5,
        axisLabel: {
          formatter: '{value}%',
          color: '#fff'
        },
        axisLine: {
          lineStyle: {
            color: '#fff'
          }
        },
        splitLine: {
          show: false
        }
      }
    ],
    series: [
      {
        name: '销售额',
        type: 'bar',
        stack: 'total',
        tooltip: {
          valueFormatter(value) {
            return `${value}万`;
          }
        },
        data: chartData.sales,
        itemStyle: {
          color: '#5470c6'
        }
      },
      {
        name: '利润',
        type: 'bar',
        stack: 'total',
        tooltip: {
          valueFormatter(value) {
            return `${value}万`;
          }
        },
        data: chartData.profit,
        itemStyle: {
          color: '#91cc75'
        }
      },
      {
        name: '增长率',
        type: 'line',
        yAxisIndex: 1,
        tooltip: {
          valueFormatter(value) {
            return `${value}%`;
          }
        },
        data: chartData.growth,
        lineStyle: {
          color: '#fac858',
          width: 3
        },
        itemStyle: {
          color: '#fac858'
        },
        symbol: 'circle',
        symbolSize: 8
      }
    ],
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '15%',
      containLabel: true
    }
  };
}

// 使用 ECharts Hook - 普通模式
const { domRef } = useEcharts(createChartOptions, {
  onRender: chart => {
    // 图表渲染完成后的回调
    console.log('Chart rendered');
  }
});

// 使用 ECharts Hook - 全屏模式
const { domRef: fullscreenDomRef } = useEcharts(createChartOptions, {
  onRender: chart => {
    // 全屏图表渲染完成后的回调
    console.log('Fullscreen chart rendered');
  }
});
</script>

<template>
  <DashboardCard
    :title="props.title"
    :title-icon="props.icon"
    :card-type="props.cardType as any"
    height="100%"
    :animation-delay="props.animationDelay"
    :show-fullscreen="true"
    theme="blue"
  >
    <!-- ECharts 图表容器 -->
    <div class="h-full w-full">
      <div ref="domRef" class="h-full w-full"></div>
    </div>

    <!-- 全屏内容 -->
    <template #fullscreen-content>
      <div class="h-full w-full p-4">
        <div class="mb-4 text-center">
          <div class="text-2xl text-white/90">{{ props.title }} - 数据分析</div>
        </div>
        <!-- 全屏模式下的图表容器 -->
        <div class="h-full w-full">
          <div ref="domRef" class="h-full w-full"></div>
        </div>
      </div>
    </template>
  </DashboardCard>
</template>
